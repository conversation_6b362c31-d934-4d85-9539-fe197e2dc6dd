# Processing Report for Adbulla

The following information could not be found in the referral documents:

- **Yes** (Needed for field with purpose: `electronic_prior_auth_yes`)
- **No** (Needed for field with purpose: `electronic_prior_auth_no`)
- **Would you like to use electronic prior authorization?** (Needed for field with purpose: `form_instruction`)
- **Phone:** (Needed for field with purpose: `aetna_medicare_advantage_phone`)
- **Fax:** (Needed for field with purpose: `aetna_medicare_advantage_fax`)
- **Phone:** (Needed for field with purpose: `aetna_virginia_dsnp_phone`)
- **Fax:** (Needed for field with purpose: `aetna_virginia_dsnp_fax`)
- **Availity:** (Needed for field with purpose: `aetna_virginia_dsnp_availity`)
- **Availity:** (Needed for field with purpose: `aetna_nj_dsnp_availity`)
- **Phone:** (Needed for field with purpose: `aetna_nj_dsnp_phone`)
- **Fax:** (Needed for field with purpose: `aetna_nj_dsnp_fax`)
- **Enrollee** (Needed for field with purpose: `request_initiated_by_enrollee`)
- **Facility** (Needed for field with purpose: `request_initiated_by_facility`)
- **Concurrent** (Needed for field with purpose: `request_type_concurrent`)
- **Extension** (Needed for field with purpose: `request_type_extension`)
- **OCA Number** (Needed for field with purpose: `prior_authorization_number`)
- **Reconsideration** (Needed for field with purpose: `request_type_reconsideration`)
- **Previous Denial Date** (Needed for field with purpose: `previous_denial_date`)
- **Original Prior Authorization Number** (Needed for field with purpose: `original_prior_authorization_number`)
- **Reason for Reconsideration** (Needed for field with purpose: `reason_for_reconsideration`)
- **Additional Information** (Needed for field with purpose: `additional_information`)
- **Yes** (Needed for field with purpose: `clinical_question_was_previously_denied_yes`)
- **No** (Needed for field with purpose: `clinical_question_was_previously_denied_no`)
- **No** (Needed for field with purpose: `is_requesting_provider_also_ordering_provider_no`)
- **Provider Type (Requesting)** (Needed for field with purpose: `provider_type_requesting`)
- **Provider Type (Ordering)** (Needed for field with purpose: `provider_type_ordering`)
- **Tax ID** (Needed for field with purpose: `ordering_provider_tax_id`)
- **Yes** (Needed for field with purpose: `clinical_question_is_patient_pregnant_yes`)
- **Unknown** (Needed for field with purpose: `clinical_question_is_patient_pregnant_unknown`)
- **Female** (Needed for field with purpose: `patient_gender_female`)
- **Yes** (Needed for field with purpose: `clinical_question_patient_has_other_insurance_yes`)
- **Other Insurance Details** (Needed for field with purpose: `other_insurance_details`)
- **Medicare Part A** (Needed for field with purpose: `other_insurance_type_medicare_part_a`)
- **Medicare Part B** (Needed for field with purpose: `other_insurance_type_medicare_part_b`)
- **Medicare Part D** (Needed for field with purpose: `other_insurance_type_medicare_part_d`)
- **Medicaid** (Needed for field with purpose: `other_insurance_type_medicaid`)
- **Commercial** (Needed for field with purpose: `other_insurance_type_commercial`)
- **Other Insurance Group Number** (Needed for field with purpose: `other_insurance_group_number`)
- **Yes** (Needed for field with purpose: `clinical_question_is_carrier_claim_yes`)
- **No** (Needed for field with purpose: `clinical_question_is_carrier_claim_no`)
- **Unknown** (Needed for field with purpose: `clinical_question_is_carrier_claim_unknown`)
- **Reauthorization** (Needed for field with purpose: `request_type_reauthorization`)
- **Yes** (Needed for field with purpose: `clinical_question_patient_has_other_coverage_yes`)
- **Unknown** (Needed for field with purpose: `clinical_question_patient_has_other_coverage_unknown`)
- **Monday** (Needed for field with purpose: `service_schedule_1_day_monday`)
- **Tuesday** (Needed for field with purpose: `service_schedule_1_day_tuesday`)
- **Wednesday** (Needed for field with purpose: `service_schedule_1_day_wednesday`)
- **Thursday** (Needed for field with purpose: `service_schedule_1_day_thursday`)
- **Friday** (Needed for field with purpose: `service_schedule_1_day_friday`)
- **Saturday** (Needed for field with purpose: `service_schedule_1_day_saturday`)
- **Yes** (Needed for field with purpose: `unmapped_checkbox_yes`)
- **Monday** (Needed for field with purpose: `service_schedule_2_day_monday`)
- **Tuesday** (Needed for field with purpose: `service_schedule_2_day_tuesday`)
- **Wednesday** (Needed for field with purpose: `service_schedule_2_day_wednesday`)
- **Thursday** (Needed for field with purpose: `service_schedule_2_day_thursday`)
- **Friday** (Needed for field with purpose: `service_schedule_2_day_friday`)
- **Saturday** (Needed for field with purpose: `service_schedule_2_day_saturday`)
- **No** (Needed for field with purpose: `unmapped_checkbox_no`)
- **No** (Needed for field with purpose: `clinical_question_has_supporting_documentation_no`)
- **Unknown** (Needed for field with purpose: `clinical_question_has_supporting_documentation_unknown`)

## Summary
- **Total Form Fields**: 323
- **Successfully Extracted**: 58 fields
- **Missing Information**: 265 fields
- **Fill Rate**: 18.0%

*Note: Many missing fields are form instructions, contact information, or conditional fields that may not apply to this specific case.*