# Processing Report for Akshay

The following information could not be found in the referral documents:

- **/** (Needed for field with purpose: `request_date_separator_1`)
- **/** (Needed for field with purpose: `request_date_separator_2`)
- **/** (Needed for field with purpose: `unidentified_separator`)
- **Indicate** (Needed for field with purpose: `request_type_option_2`)
- **/** (Needed for field with purpose: `secondary_date_separator_1`)
- **/** (Needed for field with purpose: `secondary_date_separator_2`)
- **/** (Needed for field with purpose: `secondary_date_separator_3`)
- **or** (Needed for field with purpose: `patient_height_unit_separator`)
- **cms** (Needed for field with purpose: `patient_height_unit_cms`)
- **Yes** (Needed for field with purpose: `clinical_question_has_other_coverage_yes`)
- **No** (Needed for field with purpose: `clinical_question_has_other_coverage_no`)
- **Does patient have other coverage?** (Needed for field with purpose: `question_has_other_coverage`)
- **Carrier Name:** (Needed for field with purpose: `secondary_insurance_carrier_name`)
- **If yes, provide ID#:** (Needed for field with purpose: `secondary_insurance_member_id`)
- **Yes** (Needed for field with purpose: `clinical_question_has_medicaid_yes`)
- **No** (Needed for field with purpose: `clinical_question_has_medicaid_no`)
- **Medicaid:** (Needed for field with purpose: `question_has_medicaid`)
- **Yes** (Needed for field with purpose: `insurance_info_unspecified_question_yes`)
- **No** (Needed for field with purpose: `insurance_info_unspecified_question_no`)
- **Office Contact Name:** (Needed for field with purpose: `prescriber_office_contact_name`)
- **D.O.** (Needed for field with purpose: `prescriber_credential_do_checkbox`)
- **N.P.** (Needed for field with purpose: `prescriber_credential_np_checkbox`)
- **P.A.** (Needed for field with purpose: `prescriber_credential_pa_checkbox`)
- **DEA #:** (Needed for field with purpose: `prescriber_dea_number`)
- **UPIN:** (Needed for field with purpose: `prescriber_upin`)
- **Other** (Needed for field with purpose: `prescriber_credential_other`)
- **Self-administered** (Needed for field with purpose: `drug_administration_location_self_administered`)
- **Physician's Office** (Needed for field with purpose: `drug_administration_location_physicians_office`)
- **Dispensing Pharmacy Name:** (Needed for field with purpose: `dispensing_pharmacy_name`)
- **All Records** (Needed for field with purpose: `release_of_information_all_records`)
- **Labs** (Needed for field with purpose: `release_of_information_labs`)
- **Office Visits** (Needed for field with purpose: `release_of_information_office_visits`)
- **Physician Office** (Needed for field with purpose: `place_of_service_physician_office_label`)
- **Servicing Facility Name:** (Needed for field with purpose: `servicing_facility_name`)
- **Home Infusion** (Needed for field with purpose: `place_of_service_home_infusion`)
- **Home Infusion Agency Name:** (Needed for field with purpose: `home_infusion_agency_name`)
- **Home Infusion Agency Address:** (Needed for field with purpose: `home_infusion_agency_address`)
- **Administration CPT Codes** (Needed for field with purpose: `administration_cpt_codes_checkbox`)
- **Servicing Facility Address:** (Needed for field with purpose: `servicing_facility_address`)
- **Servicing Facility Phone Number:** (Needed for field with purpose: `servicing_facility_phone_number`)
- **Retail** (Needed for field with purpose: `pharmacy_type_retail`)
- **Other** (Needed for field with purpose: `pharmacy_type_other`)
- **Specialty** (Needed for field with purpose: `pharmacy_type_specialty`)
- **Other** (Needed for field with purpose: `place_of_service_other`)
- **Pharmacy Name:** (Needed for field with purpose: `pharmacy_name`)
- **Pharmacy Address:** (Needed for field with purpose: `pharmacy_address`)
- **Pharmacy Fax Number:** (Needed for field with purpose: `pharmacy_fax_number`)
- **Pharmacy TIN:** (Needed for field with purpose: `pharmacy_tin`)
- **Pharmacy PIN:** (Needed for field with purpose: `pharmacy_pin`)
- **Secondary Diagnosis ICD Code:** (Needed for field with purpose: `secondary_diagnosis_icd_code`)
- **Tertiary Diagnosis ICD Code:** (Needed for field with purpose: `tertiary_diagnosis_icd_code`)
- **No** (Needed for field with purpose: `clinical_question_1_no`)
- **Yes** (Needed for field with purpose: `clinical_question_1_yes`)
- **No** (Needed for field with purpose: `clinical_question_2_no`)
- **Yes** (Needed for field with purpose: `clinical_question_2_yes`)
- **No** (Needed for field with purpose: `clinical_question_3_no`)
- **Yes** (Needed for field with purpose: `clinical_question_3_yes`)
- **No** (Needed for field with purpose: `clinical_question_tb_tested_no`)
- **weeks** (Needed for field with purpose: `duration_unit_weeks_1`)
- **weeks** (Needed for field with purpose: `duration_unit_weeks_2`)
- **weeks** (Needed for field with purpose: `duration_unit_weeks_3`)
- **Latent and treated** (Needed for field with purpose: `clinical_question_tb_status_latent_and_treated`)
- **Latent and untreated** (Needed for field with purpose: `clinical_question_tb_status_latent_and_untreated`)
- **Latent and treated** (Needed for field with purpose: `clinical_question_tb_status_latent_and_treated_duplicate`)
- **Active** (Needed for field with purpose: `clinical_question_tb_status_active`)
- **Yes** (Needed for field with purpose: `clinical_question_4_yes`)
- **No** (Needed for field with purpose: `clinical_question_4_no`)
- **Yes** (Needed for field with purpose: `clinical_question_5_yes`)
- **No** (Needed for field with purpose: `clinical_question_5_no`)
- **Yes** (Needed for field with purpose: `clinical_question_6_yes`)
- **No** (Needed for field with purpose: `clinical_question_6_no`)
- **Yes** (Needed for field with purpose: `clinical_question_7_yes`)
- **No** (Needed for field with purpose: `clinical_question_7_no`)
- **weeks** (Needed for field with purpose: `duration_unit_weeks_4`)
- **weeks** (Needed for field with purpose: `duration_unit_weeks_5`)
- **weeks** (Needed for field with purpose: `duration_unit_weeks_6`)
- **Yes** (Needed for field with purpose: `clinical_question_8_yes`)
- **No** (Needed for field with purpose: `clinical_question_8_no`)

## Summary
- **Total Form Fields**: 118
- **Successfully Extracted**: 48 fields
- **Missing Information**: 70 fields
- **Fill Rate**: 40.7%

*Note: Many missing fields are form separators, conditional checkboxes, or clinical questions that may not apply to this specific case.*